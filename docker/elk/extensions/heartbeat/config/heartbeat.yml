## Heartbeat configuration
## https://github.com/elastic/beats/blob/main/deploy/docker/heartbeat.docker.yml
#

name: heartbeat

heartbeat.monitors:
- type: http
  schedule: '@every 5s'
  urls:
    - http://elasticsearch:9200
  username: heartbeat_internal
  password: ${HEARTBEAT_INTERNAL_PASSWORD}

- type: icmp
  schedule: '@every 5s'
  hosts:
    - elasticsearch

processors:
- add_cloud_metadata: ~

monitoring:
  enabled: true
  elasticsearch:
    username: beats_system
    password: ${BEATS_SYSTEM_PASSWORD}

output.elasticsearch:
  hosts: [ http://elasticsearch:9200 ]
  username: heartbeat_internal
  password: ${HEARTBEAT_INTERNAL_PASSWORD}

## HTTP endpoint for health checking
## https://www.elastic.co/docs/reference/beats/heartbeat/http-endpoint
#

http:
  enabled: true
  host: 0.0.0.0
