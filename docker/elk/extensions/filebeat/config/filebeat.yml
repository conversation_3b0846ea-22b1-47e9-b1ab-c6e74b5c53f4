## Filebeat configuration
## https://github.com/elastic/beats/blob/main/deploy/docker/filebeat.docker.yml
#

name: filebeat

filebeat.config:
  modules:
    path: ${path.config}/modules.d/*.yml
    reload.enabled: false

filebeat.autodiscover:
  providers:
    # The Docker autodiscover provider automatically retrieves logs from Docker
    # containers as they start and stop.
    - type: docker
      hints.enabled: true
      hints.default_config:
        type: container
        paths:
          - /var/lib/docker/containers/${data.container.id}/*-json.log
      templates:
        - condition:
            contains:
              docker.container.image: elasticsearch
          config:
            - module: elasticsearch
              server:
                input:
                  type: container
                  paths:
                    - /var/lib/docker/containers/${data.container.id}/*-json.log

processors:
  - add_cloud_metadata: ~

monitoring:
  enabled: true
  elasticsearch:
    username: beats_system
    password: ${BEATS_SYSTEM_PASSWORD}

output.elasticsearch:
  hosts: [ http://elasticsearch:9200 ]
  username: filebeat_internal
  password: ${FILEBEAT_INTERNAL_PASSWORD}

## HTTP endpoint for health checking
## https://www.elastic.co/docs/reference/beats/filebeat/http-endpoint
#

http:
  enabled: true
  host: 0.0.0.0
