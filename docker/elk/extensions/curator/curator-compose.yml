services:
  curator:
    build:
      context: extensions/curator/
    init: true
    volumes:
      - ./extensions/curator/config/curator.yml:/.curator/curator.yml:ro,Z
      - ./extensions/curator/config/delete_log_files_curator.yml:/.curator/delete_log_files_curator.yml:ro,Z
    environment:
      ELASTIC_PASSWORD: ${ELASTIC_PASSWORD:-}
    networks:
      - elk
    depends_on:
      - elasticsearch
