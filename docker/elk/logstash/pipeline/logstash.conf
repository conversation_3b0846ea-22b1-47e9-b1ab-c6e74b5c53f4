input {
	# beats {
	# 	port => 5044
	# }

	tcp {
		port => 50000
    codec => json_lines {
      target => "[document]"
    }
	}
}

filter {

  # Replace null to "null" inside document.data
    ruby {
    code => "
      def replace_nulls(obj)
        case obj
        when Hash
          obj.each do |k, v|
            obj[k] = replace_nulls(v)
          end
        when Array
          obj.map! { |v| replace_nulls(v) }
        when nil
          'null'
        else
          obj
        end
      end

      if event.get('[document][data]').is_a?(Hash)
        cleaned = replace_nulls(event.get('[document][data]'))
        event.set('[document][data]', cleaned)
      end
    "
  }

	# convert json data to string (rawData field) 
  ruby {
    code => "
      require 'json'
      if event.get('[document][data]').is_a?(Hash)
        event.set('[document][rawData]', JSON.generate(event.get('[document][data]')))
      end
    "
  }
}

output {
	elasticsearch {
		hosts => "elasticsearch:9200"
		user => "logstash_internal"
		password => "${LOGSTASH_INTERNAL_PASSWORD}"

		# register data stream manually in Kibana
		# PUT _data_stream/logs-easy-flow-core

		# then add the following privileges to the logstash_writer role in 
		# Management -> Stack Management -> Security -> Roles
		# logstash_writer -> Indices -> "logs-*"

		data_stream => "true"
    data_stream_type => "logs"
    data_stream_dataset => "easy-flow"
    data_stream_namespace => "core"
	}
}
