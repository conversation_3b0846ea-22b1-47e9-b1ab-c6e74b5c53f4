/* eslint-disable @typescript-eslint/no-explicit-any */
import { DynamicModule, Module } from '@nestjs/common';
import { Redis } from 'ioredis';
import { RedisLockService } from './redis-lock.service';
import { setRedisLockService } from './redis.module.context';

export const REDIS_CLIENT = Symbol('REDIS_CLIENT');

export const REDIS_SHUTDOWN_SERVICE = Symbol('REDIS_SHUTDOWN_SERVICE');

type RedisModuleOptions = {
  host?: string;
  port?: number;
};

type RedisModuleAsyncOptions = {
  global?: boolean;
  useFactory: (...args: any[]) => Promise<RedisModuleOptions> | RedisModuleOptions;
  inject?: any[];
};

@Module({})
export class RedisModule {
  static forRootAsync(options: RedisModuleAsyncOptions): DynamicModule {
    return {
      module: RedisModule,
      global: options.global,
      providers: [
        {
          provide: REDIS_CLIENT,
          inject: options.inject,
          useFactory: async (...args: any[]): Promise<Redis> => {
            const { host, port }: RedisModuleOptions = await options.useFactory(...args);

            const client: Redis = new Redis({ host, port });

            await new Promise<void>((resolve, reject) => {
              client.once('ready', resolve);
              client.once('error', reject);
            });

            return client;
          },
        },

        {
          provide: RedisLockService,
          inject: [REDIS_CLIENT],
          useFactory: (redis: Redis) => {
            const lockService = new RedisLockService(redis);
            setRedisLockService(lockService);
            return lockService;
          },
        },

        {
          provide: REDIS_SHUTDOWN_SERVICE,
          inject: [REDIS_CLIENT],
          useFactory: (redis: Redis) => {
            return {
              async onApplicationShutdown() {
                await redis.quit();
              },
            };
          },
        },
      ],
      exports: [REDIS_CLIENT, RedisLockService],
    };
  }
}
