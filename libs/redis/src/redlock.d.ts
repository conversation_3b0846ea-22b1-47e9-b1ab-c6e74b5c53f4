/* eslint-disable @typescript-eslint/no-explicit-any */

// Redlock module declaration to avoid type issues
declare module 'redlock' {
  import { EventEmitter } from 'events';
  import { Redis as IORedisClient, Cluster as IORedisCluster } from 'ioredis';

  type Client = IORedisClient | IORedisCluster;

  export interface Settings {
    readonly driftFactor: number;
    readonly retryCount: number;
    readonly retryDelay: number;
    readonly retryJitter: number;
    readonly automaticExtensionThreshold: number;
  }

  export class ResourceLockedError extends Error {
    readonly message: string;
    constructor(message: string);
  }

  export class ExecutionError extends Error {
    readonly message: string;
    readonly attempts: ReadonlyArray<Promise<any>>;
    constructor(message: string, attempts: ReadonlyArray<Promise<any>>);
  }

  export class Lock {
    readonly redlock: Redlock;
    readonly resources: string[];
    readonly value: string;
    readonly attempts: ReadonlyArray<Promise<any>>;
    expiration: number;
    constructor(
      redlock: Redlock,
      resources: string[],
      value: string,
      attempts: ReadonlyArray<Promise<any>>,
      expiration: number,
    );
    release(): Promise<any>;
    extend(duration: number): Promise<Lock>;
  }

  export default class Redlock extends EventEmitter {
    constructor(clients: Client[], settings?: Partial<Settings>);
    acquire(resources: string[], duration: number): Promise<Lock>;
    using<T>(resources: string[], duration: number, routine: (signal: any) => Promise<T>): Promise<T>;
    release(lock: Lock): Promise<any>;
    extend(lock: Lock, duration: number): Promise<Lock>;
    quit(): Promise<void>;
  }
}
