import { z } from 'zod';

export const FlowProcessSchema = z.object({
  id: z.coerce.string().describe('Process id'),

  name: z.string().describe('Process name'),

  description: z.string().nullable().describe('Process description'),

  parentId: z.coerce.string().nullable().describe('Process parent id'),

  prevId: z.coerce.string().nullable().describe('Process previous id'),

  mPath: z.string().describe('Process materialized path'),
});

export type FlowProcessModel = z.infer<typeof FlowProcessSchema>;
