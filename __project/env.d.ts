/* eslint-disable @typescript-eslint/no-explicit-any */

type Class<T = unknown, P = unknown[]> = new (...args: P) => T;

type Optional<T, K extends keyof T> = Omit<T, K> & Partial<T>;

type UnknownProperties<T> = { [K in keyof T]: unknown };

type NodeCallback = (err: null | Error, data?: any) => void;

type UseCase = {
  execute: (...args: any) => any;
};

type EntityId = string | number;

type NumericEntityId = number;
