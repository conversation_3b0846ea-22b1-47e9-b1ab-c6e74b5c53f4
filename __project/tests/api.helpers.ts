import { HttpStatus } from '@nestjs/common';
import { Response } from 'supertest';
import request from 'supertest';
import { App } from 'supertest/types';
import { ApiResponsePayload, ApiErrorPayload } from '@libs/common/api';

export const expectSuccessfulApiResponse = (res: Response) => {
  const body = res.body as ApiResponsePayload<unknown>;
  expect(body).toHaveProperty('success', true);
  expect(body).toHaveProperty('statusCode', res.statusCode);
  expect(body).toHaveProperty('message');
};

export const expectFailedApiResponse = (res: Response, error?: string) => {
  const body = res.body as ApiErrorPayload;
  expect(body).toHaveProperty('success', false);
  expect(body).toHaveProperty('statusCode', res.statusCode);
  expect(body).toHaveProperty('message');
  if (error) {
    expect(body).toHaveProperty('error', error);
  }
};

export const testInvalidIdParam = (
  app: App,
  method: 'delete' | 'get' | 'put' | 'post' | 'patch',
  path: string,
  data: null | object,
) => {
  return request(app)
    [method](path)
    .send(data as object)
    .expect(HttpStatus.BAD_REQUEST)
    .expect(res => expectFailedApiResponse(res, 'INVALID_ID'));
};

export const testResourceNotFound = (
  app: App,
  method: 'delete' | 'get' | 'put' | 'post' | 'patch',
  path: string,
  data: null | object,
  error: string,
) => {
  return request(app)
    [method](path)
    .send(data as object)
    .expect(HttpStatus.NOT_FOUND)
    .expect(res => expectFailedApiResponse(res, error));
};
