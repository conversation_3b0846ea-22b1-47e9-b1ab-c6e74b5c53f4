import { config } from 'dotenv';
import minimist from 'minimist';
import { execSync } from 'node:child_process';
import { existsSync, readdirSync } from 'node:fs';
import { join } from 'node:path';

/**
 * Run e2e tests for specific app.
 *
 * Example: npm run test:e2e -- --app core
 * or shortly: npm run test:e2e -- -a core
 *
 * Also you can pass any other arguments to jest, e.g.
 * npm run test:e2e -- -a core --watch
 */

// Get app name from args
const args = process.argv.slice(2);

const { a, app, ...restArgs } = minimist(args) as { a?: string; app?: string };

const appName = a ?? app;

// Get all available apps
const appsDir = './apps';
const availableApps = readdirSync(appsDir).filter(appName => {
  const configPath = join(appsDir, appName, 'test');
  return existsSync(configPath);
});

// Validate app name argument
if (!appName) {
  console.log('❌ App name is required to run e2e tests. Use -app <app-name>');
  console.log('📦 Available apps: ' + availableApps.join(', '));
  process.exit(1);
}

// Validate if app exists
if (!availableApps.includes(appName)) {
  console.log(`❌ App "${appName}" not found`);
  console.log('📦 Available apps: ' + availableApps.join(', '));
  process.exit(1);
}

// Load env variables from app's env.test file
config({ path: `./apps/${appName}/.env.test` });

const forwardedArgs = Object.entries(restArgs)
  .flatMap(([key, value]) => {
    return [`--${key}`, value === true ? '' : String(value)];
  })
  .filter(arg => arg !== '' && arg !== '--_');

// Run e2e tests
execSync(`cross-env E2E_APP=${appName} jest ${forwardedArgs.join(' ')}`, { stdio: 'inherit' });
