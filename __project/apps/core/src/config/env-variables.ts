import { z } from 'zod';

export const EnvVariablesSchema = z.object({
  // PostgreSQL
  PG_USER: z.string(),
  PG_PASSWORD: z.string(),
  PG_DB: z.string(),
  PG_HOST: z.string(),
  PG_PORT: z.coerce.number(),

  LOG_LEVEL: z.string().default('trace'),

  // Redis
  REDIS_HOST: z.string(),
  REDIS_PORT: z.coerce.number(),

  // Elasticsearch/Logstash configuration
  LOGSTASH_HOST: z.string().optional(),
  LOGSTASH_PORT: z.coerce.number().default(50000),
});

export type EnvironmentVariables = z.infer<typeof EnvVariablesSchema>;
