import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';
import { patchNestJsSwagger } from 'nestjs-zod';
import { CoreModule } from './core.module';

patchNestJsSwagger();

async function bootstrap() {
  const app = await NestFactory.create(CoreModule);

  // TODO
  // app.enableShutdownHooks();

  app.setGlobalPrefix('/api');

  /** configure logger */
  const logger = app.get(Logger);
  app.useLogger(logger);

  /** swagger setup */
  const openApiConfig = new DocumentBuilder()
    .setTitle('API')
    .setVersion('0.0.0')
    // .addBearerAuth({ type: 'http', scheme: 'bearer' }, ACCESS_TOKEN_SECURITY_KEY)
    .build();

  const openApi = SwaggerModule.createDocument(app, openApiConfig);
  /** webpage: /swagger */
  SwaggerModule.setup('swagger', app, openApi, {
    jsonDocumentUrl: 'swagger/json',
  });

  const config = app.get(ConfigService);

  await app.listen(config.get('PORT') ?? 3000);
  logger.log(`Server started on port ${await app.getUrl()}. Swagger: ${await app.getUrl()}/swagger`);
}

void bootstrap();
