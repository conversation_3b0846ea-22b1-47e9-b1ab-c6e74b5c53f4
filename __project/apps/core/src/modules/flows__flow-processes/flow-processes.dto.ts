import { createZodDto } from 'nestjs-zod';
import { FlowProcessSchema } from '@core/models';

export class FlowProcessDto extends createZodDto(FlowProcessSchema) {}

const CreateFlowProcessSchema = FlowProcessSchema.pick({
  name: true,
  description: true,
  parentId: true,
  prevId: true,
});

export class CreateFlowProcessDto extends createZodDto(CreateFlowProcessSchema) {}

const UpdateFlowProcessSchema = FlowProcessSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowProcessDto extends createZodDto(UpdateFlowProcessSchema) {}

const PartialUpdateFlowProcessSchema = UpdateFlowProcessSchema.partial();

export class PartialUpdateFlowProcessDto extends createZodDto(PartialUpdateFlowProcessSchema) {}

const MoveFlowProcessSchema = FlowProcessSchema.pick({
  parentId: true,
  prevId: true,
});

export class Move<PERSON>lowProcessDto extends createZod<PERSON><PERSON>(MoveFlowProcessSchema) {}
