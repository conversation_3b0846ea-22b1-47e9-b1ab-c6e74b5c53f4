import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiOperation, ApiParam } from '@nestjs/swagger';
import { z } from 'zod';
import { FlowProcessSchema } from '@core/models';
import {
  ApiError,
  ApiSuccessfulResponse,
  EMPTY_RESPONSE,
  HttpResponse,
  SanitizeResponseWithZod,
} from '@libs/common/api';
import {
  CreateFlowProcessDto,
  FlowProcessDto,
  MoveFlowProcessDto,
  PartialUpdateFlowProcessDto,
  UpdateFlowProcessDto,
} from './flow-processes.dto';
import {
  CreateFlowProcessUseCase,
  DeleteFlowProcessByIdUseCase,
  GetAllFlowProcessesUseCase,
  GetFlowProcessByIdUseCase,
  MoveFlowProcessUseCase,
  UpdateFlowProcessUseCase,
} from './use-cases';

const ApiParamFlowProcessId = () =>
  ApiParam({ name: 'processId', required: true, description: 'Process identifier', type: 'string' });

@Controller('flows/:flowId/processes')
@ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' })
export class FlowProcessesController {
  constructor(
    private readonly createFlowProcessUseCase: CreateFlowProcessUseCase,
    private readonly updateFlowProcessUseCase: UpdateFlowProcessUseCase,
    private readonly deleteFlowProcessByIdUseCase: DeleteFlowProcessByIdUseCase,
    private readonly moveFlowProcessUseCase: MoveFlowProcessUseCase,
    private readonly getFlowProcessByIdUseCase: GetFlowProcessByIdUseCase,
    private readonly getAllFlowProcessesUseCase: GetAllFlowProcessesUseCase,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create process' })
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Process created', FlowProcessDto)
  @ApiError(HttpStatus.CONFLICT, 'Parent process not found', 'PARENT_PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async createProcess(@Param('flowId') flowId: string, @Body() dto: CreateFlowProcessDto) {
    const process = await this.createFlowProcessUseCase.execute({ flowId, dto });
    return new HttpResponse({
      statusCode: HttpStatus.CREATED,
      data: process,
      message: 'Process created',
    });
  }

  @Put(':processId')
  @ApiOperation({ summary: 'Update process' })
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Process updated', FlowProcessDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async updateProcess(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Body() dto: UpdateFlowProcessDto,
  ) {
    const process = await this.updateFlowProcessUseCase.execute({ processId, dto });
    return new HttpResponse({ data: process, message: 'Process updated' });
  }

  @Patch(':processId')
  @ApiOperation({ summary: 'Partial update process' })
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Process updated', FlowProcessDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async partialUpdateProcess(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Body() dto: PartialUpdateFlowProcessDto,
  ) {
    await this.updateFlowProcessUseCase.execute({ processId, dto });
    return new HttpResponse({ message: 'Process updated' });
  }

  @Delete(':processId')
  @ApiOperation({ summary: 'Delete process' })
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Process deleted', FlowProcessDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async deleteProcess(@Param('flowId') flowId: string, @Param('processId') processId: string) {
    const process = await this.deleteFlowProcessByIdUseCase.execute({ processId, flowId });
    return new HttpResponse({ data: process, message: 'Process deleted' });
  }

  @Post(':processId/move')
  @ApiOperation({ summary: 'Move process' })
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Process moved', FlowProcessDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async moveProcess(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Body() dto: MoveFlowProcessDto,
  ) {
    const process = await this.moveFlowProcessUseCase.execute({ processId, flowId, dto });
    return new HttpResponse({ data: process, message: 'Process moved' });
  }

  @Get(':processId')
  @ApiOperation({ summary: 'Get process' })
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Process found', FlowProcessDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Process not found', 'PROCESS_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessSchema)
  async getProcess(@Param('flowId') flowId: string, @Param('processId') processId: string) {
    const process = await this.getFlowProcessByIdUseCase.execute({ processId });
    return new HttpResponse({ data: process, message: 'Process found' });
  }

  @Get()
  @ApiOperation({ summary: 'Get all flow processes' })
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow processes found', [FlowProcessDto])
  @SanitizeResponseWithZod(z.array(FlowProcessSchema))
  async getProcesses(@Param('flowId') flowId: string) {
    const processes = await this.getAllFlowProcessesUseCase.execute({ flowId });
    return new HttpResponse({ data: processes, message: 'Flow processes found' });
  }
}
