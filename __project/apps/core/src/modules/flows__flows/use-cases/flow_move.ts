import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IsNull } from 'typeorm';
import { DatabaseService, Flow } from '@core/database';
import { NumericId } from '@libs/common/database';
import { RedisLock } from '@libs/redis';
import { MoveFlowDto } from '../flows.dto';

@Injectable()
export class MoveFlowUseCase implements UseCase {
  private readonly logger = new Logger(MoveFlowUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  // TODO: add project scope

  @RedisLock(_args => 'project-x--flows')
  async execute({ flowId: _flowId, dto }: { flowId: string; dto: MoveFlowDto }): Promise<Flow> {
    const flowId = NumericId(_flowId);
    /** targetPrevId is a new destination of the moving flow */
    const targetPrevId = NumericId(dto.prevId);

    this.logger.verbose({ msg: 'Started moving flow', data: { flowId, dto } });

    if (flowId === targetPrevId)
      throw new BadRequestException('Cannot move flow to itself', 'CANNOT_MOVE_TO_ITSELF');

    // TODO: optimize

    const [movingFlow, nextFlow, targetNextFlow] = await this.db.flows.manager.transaction(
      async entityManager => {
        this.logger.verbose({ msg: 'Finding data' });

        // TODO: find many and then use find by result array

        const [movingFlow, nextFlow, targetNextFlow] = await Promise.all([
          /** Moving flow */
          entityManager.findOne(Flow, {
            where: { id: flowId },
          }),

          /** Next flow (it is the flow with prevId = movingFlow.id) */
          entityManager.findOne(Flow, {
            where: { prevId: flowId },
          }),

          /** Target next flow (it is the flow with prevId = targetPrevId) */
          /** We don't need the target flow itself, we just need to update next flow's prevId */
          entityManager.findOne(Flow, {
            where: {
              prevId: targetPrevId ? targetPrevId : IsNull(),
            },
          }),
        ]);

        this.logger.verbose({
          msg: 'Data found',
          data: {
            flow: movingFlow,
            nextFlow,
            targetNextFlow,
          },
        });

        if (!movingFlow) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

        if (movingFlow.prevId === targetPrevId) {
          this.logger.verbose({ msg: 'Flow is already moved' });
          return [movingFlow, nextFlow, targetNextFlow];
        }

        /** Shift nextFlow above */
        if (nextFlow) {
          this.logger.verbose({ msg: 'Next flow found', data: { flowId: nextFlow.id } });

          this.logger.verbose({
            msg: 'Shifting next flow above',
            data: { flowId: nextFlow.id, prevId: movingFlow.prevId },
          });

          nextFlow.prevId = movingFlow.prevId;
          await entityManager.save(Flow, nextFlow);
        } else {
          this.logger.verbose({ msg: 'No next flow found' });
        }

        /** Move flow */
        this.logger.verbose({
          msg: 'Moving flow to new parent and prev flow',
          data: {
            flowId: movingFlow.id,
            targetPrevId,
          },
        });

        movingFlow.prevId = targetPrevId;

        await entityManager.save(Flow, movingFlow);

        this.logger.verbose({ msg: 'Flow moved', data: { flowId: movingFlow.id } });

        /** Shift targetNextFlow below */
        if (targetNextFlow) {
          this.logger.verbose({
            msg: 'Target next flow found',
            data: { flowId: targetNextFlow.id },
          });

          this.logger.verbose({
            msg: 'Shifting target flow below',
            data: { flowId: targetNextFlow.id, prevId: movingFlow.id },
          });

          targetNextFlow.prevId = movingFlow.id;
          await entityManager.save(Flow, targetNextFlow);
        } else {
          this.logger.verbose({ msg: 'No target next flow found' });
        }

        return [movingFlow, nextFlow, targetNextFlow];
      },
    );

    this.logger.log({
      msg: 'Flow moved',
      data: {
        incomingData: { flowId, dto },
        modifiedFlow: movingFlow,
        modifiedData: {
          nextFlow,
          targetNextFlow,
        },
      },
    });

    return movingFlow;
  }
}
