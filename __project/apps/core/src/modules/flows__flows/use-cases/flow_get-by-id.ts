import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, Flow } from '@core/database';
import { NumericId } from '@libs/common/database';

@Injectable()
export class GetFlowByIdUseCase implements UseCase {
  private readonly logger = new Logger(GetFlowByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ flowId: _flowId }: { flowId: string }): Promise<Flow> {
    const flowId = NumericId(_flowId);

    this.logger.verbose({ msg: 'Started getting flow', data: { flowId } });

    const flow = await this.db.flows.findOne({
      where: { id: flowId },
    });

    if (!flow) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

    this.logger.verbose({
      msg: 'Flow found',
      data: {
        incomingData: { flowId },
        foundFlow: flow,
      },
    });

    return flow;
  }
}
