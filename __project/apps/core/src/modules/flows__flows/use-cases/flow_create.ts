import { Injectable, Logger } from '@nestjs/common';
import { IsNull, Not } from 'typeorm';
import { DatabaseService, Flow } from '@core/database';
import { NumericId } from '@libs/common/database';
import { RedisLock, RedisLockService } from '@libs/redis';
import { CreateFlowDto } from '../flows.dto';
import { FlowsService } from '../flows.service';

@Injectable()
export class CreateFlowUseCase implements UseCase {
  private readonly logger = new Logger(CreateFlowUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly flowsService: FlowsService,
    private readonly mutex: RedisLockService,
  ) {}

  @RedisLock(_args => 'project-x--flows')
  async execute({ dto }: { dto: CreateFlowDto }): Promise<Flow> {
    const { name, description, prevId: _prevId } = dto;
    const prevId = NumericId(_prevId);

    this.logger.verbose({ msg: 'Started creating flow', data: { dto } });

    const [flow, existingRoot] = await this.db.flows.manager.transaction(async entityManager => {
      // Create flow entity
      const flow = entityManager.create(
        Flow,
        this.flowsService.normalizeEntityValues(new Flow(), {
          name,
          description,
          prevId,
        }),
      );

      this.logger.verbose({ msg: 'Flow entity created', data: flow });

      await entityManager.save(flow);

      this.logger.verbose({ msg: 'Flow saved', data: flow });

      this.logger.verbose({ msg: 'Finding flow with same prevId', data: { prevId } });

      // Find flow with same prevId (root flow)
      const existingRoot = await entityManager.findOne(Flow, {
        where: {
          id: Not(flow.id),
          prevId: prevId === null ? IsNull() : prevId,
        },
      });

      // Shift existingRoot below if it exists
      if (existingRoot) {
        this.logger.verbose({ msg: 'Found flow with same prevId', data: existingRoot });

        this.logger.verbose({
          msg: 'Updating prevId for existing flow with same prevId',
          data: { flowId: existingRoot.id, newPrevId: flow.id },
        });

        existingRoot.prevId = flow.id;
        await entityManager.save(existingRoot);
      } else {
        this.logger.verbose({ msg: 'No flow with same prevId found' });
      }

      return [flow, existingRoot];
    });

    this.logger.log({
      msg: 'Flow created',
      data: {
        incomingData: { dto },
        createdFlow: flow,
        modifiedData: { existingRoot },
      },
    });

    return flow;
  }
}
