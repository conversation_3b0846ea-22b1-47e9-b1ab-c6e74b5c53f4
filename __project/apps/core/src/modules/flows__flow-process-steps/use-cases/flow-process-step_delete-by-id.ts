import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Like } from 'typeorm';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { NumericId } from '@libs/common/database';

@Injectable()
export class DeleteFlowProcessStepByIdUseCase implements UseCase {
  private readonly logger = new Logger(DeleteFlowProcessStepByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ processId, stepId }: { processId: string; stepId: string }): Promise<FlowProcessStep> {
    this.logger.verbose({ msg: 'Started deleting step', data: { processId, stepId } });

    const [deletedStep, nextStep] = await this.db.flowProcessSteps.manager.transaction(
      async entityManager => {
        this.logger.verbose({ msg: 'Finding step to delete', data: { stepId } });

        /** Find step to delete */
        const stepToDelete = await entityManager.findOne(FlowProcessStep, {
          where: { id: NumericId(stepId) },
        });

        if (!stepToDelete) throw new NotFoundException('Step not found', 'STEP_NOT_FOUND');

        this.logger.verbose({ msg: 'Step found', data: stepToDelete });

        this.logger.verbose({ msg: 'Deleting step', data: { stepId: stepToDelete.id } });

        /** Delete step */
        await entityManager.delete(FlowProcessStep, stepToDelete.id);

        this.logger.verbose({ msg: 'Step deleted', data: { stepId: stepToDelete.id } });

        this.logger.verbose({ msg: 'Deleting children (mPath/%)', data: { mPath: stepToDelete.mPath } });

        /** Delete children */
        await entityManager.delete(FlowProcessStep, {
          flowProcess: { id: NumericId(processId) },
          mPath: Like(`${stepToDelete.mPath}/%`),
        });

        this.logger.verbose({ msg: 'Children deleted', data: { mPath: stepToDelete.mPath } });

        this.logger.verbose({
          msg: 'Finding next step to update prevId',
          data: { prevId: stepToDelete.id },
        });

        /** Update next step prevId */
        const nextStep = await entityManager.findOne(FlowProcessStep, {
          where: {
            flowProcess: { id: NumericId(processId) },
            prevId: stepToDelete.id,
          },
        });

        if (nextStep) {
          this.logger.verbose({ msg: 'Next step found', data: nextStep });

          this.logger.verbose({
            msg: 'Updating next step prevId',
            data: { prevId: stepToDelete.prevId },
          });

          nextStep.prevId = stepToDelete.prevId;
          await entityManager.save(FlowProcessStep, nextStep);
        } else {
          this.logger.verbose({ msg: 'No next step found' });
        }

        return [stepToDelete, nextStep];
      },
    );

    this.logger.log({
      msg: 'Step deleted with its children',
      data: {
        incomingData: { processId, stepId },
        deletedStep,
        modifiedData: { nextStep },
      },
    });

    return deletedStep;
  }
}
