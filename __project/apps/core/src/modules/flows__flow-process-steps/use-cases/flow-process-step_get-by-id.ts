import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { NumericId } from '@libs/common/database';

@Injectable()
export class GetFlowProcessStepByIdUseCase implements UseCase {
  private readonly logger = new Logger(GetFlowProcessStepByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ stepId }: { stepId: string }): Promise<FlowProcessStep> {
    this.logger.verbose({ msg: 'Started getting step', data: { stepId } });

    const step = await this.db.flowProcessSteps.findOne({
      where: { id: NumericId(stepId) },
    });

    if (!step) throw new NotFoundException('Step not found', 'STEP_NOT_FOUND');

    this.logger.verbose({
      msg: 'Step found',
      data: {
        incomingData: { stepId },
        foundStep: step,
      },
    });

    return step;
  }
}
