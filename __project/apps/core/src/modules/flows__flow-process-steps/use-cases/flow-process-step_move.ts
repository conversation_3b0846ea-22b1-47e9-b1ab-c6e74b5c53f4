import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IsNull, Like } from 'typeorm';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { NumericId } from '@libs/common/database';
import { MoveFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class MoveFlowProcessStepUseCase implements UseCase {
  private readonly logger = new Logger(MoveFlowProcessStepUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly stepsService: FlowProcessStepsService,
  ) {}

  async execute({
    stepId,
    processId,
    dto,
  }: {
    stepId: string;
    processId: string;
    dto: MoveFlowProcessStepDto;
  }): Promise<FlowProcessStep> {
    /** This is a data with a new destination of the moving step  */
    const { parentId: targetParentId, prevId: targetPrevId } = dto;

    this.logger.verbose({ msg: 'Started moving step', data: { stepId, processId, dto } });

    // TODO: optimize

    const [movingStep, nextStep, targetNextStep, targetParentStep] =
      await this.db.flowProcesses.manager.transaction(async entityManager => {
        this.logger.verbose({ msg: 'Finding data' });

        // TODO: find many and then use find by result array

        const [movingStep, nextStep, targetNextStep, targetParentStep] = await Promise.all([
          /** Moving step */
          entityManager.findOne(FlowProcessStep, {
            where: { id: NumericId(stepId) },
          }),

          /** Next step (it is the step with prevId = movingStep.id) */
          entityManager.findOne(FlowProcessStep, {
            where: { flowProcess: { id: NumericId(processId) }, prevId: NumericId(stepId) },
          }),

          /** Target next step (it is the step with prevId = targetPrevId) */
          /** We don't need the target step itself, we just need to update next step's prevId */
          entityManager.findOne(FlowProcessStep, {
            where: {
              flowProcess: { id: NumericId(processId) },
              parentId: targetParentId ? NumericId(targetParentId) : IsNull(),
              prevId: targetPrevId ? NumericId(targetPrevId) : IsNull(),
            },
          }),
          /** Target parent step */
          targetParentId
            ? entityManager.findOne(FlowProcessStep, {
                where: { id: NumericId(targetParentId) },
              })
            : null,
        ]);

        this.logger.verbose({
          msg: 'Data found',
          data: { step: movingStep, nextStep, targetNextStep, targetParentStep },
        });

        if (!movingStep) throw new NotFoundException('Step not found', 'STEP_NOT_FOUND');

        /** Shift nextStep above */
        if (nextStep) {
          this.logger.verbose({ msg: 'Next step found', data: { stepId: nextStep.id } });

          this.logger.verbose({
            msg: 'Shifting next step above',
            data: { stepId: nextStep.id, prevId: movingStep.prevId },
          });

          nextStep.prevId = movingStep.prevId;
          await entityManager.save(FlowProcessStep, nextStep);
        } else {
          this.logger.verbose({ msg: 'No next step found' });
        }

        /** Move step */
        const newStepMPath = await this.stepsService.generateUniqueMaterializedPath(entityManager, {
          processId,
          parentMPath: targetParentStep ? targetParentStep.mPath : undefined,
        });

        this.logger.verbose({
          msg: 'Moving step to new parent and prev step',
          data: {
            stepId: movingStep.id,
            stepMPath: newStepMPath,
            targetParentId,
            targetPrevId,
          },
        });

        const currentStepMPath = movingStep.mPath;

        movingStep.parentId = NumericId(targetParentId);
        movingStep.prevId = NumericId(targetPrevId);
        movingStep.mPath = newStepMPath;

        await entityManager.save(FlowProcessStep, movingStep);

        this.logger.verbose({ msg: 'Step moved', data: { stepId: movingStep.id } });

        /** Update mPath for children */
        this.logger.verbose({ msg: 'Updating mPath for children', data: { currentStepMPath, newStepMPath } });

        await entityManager.update(
          FlowProcessStep,
          {
            flowProcess: { id: NumericId(processId) },
            mPath: Like(`${currentStepMPath}/%`),
          },
          {
            mPath: newStepMPath,
          },
        );

        /** Shift targetNextStep below */
        if (targetNextStep) {
          this.logger.verbose({ msg: 'Target next step found', data: { stepId: targetNextStep.id } });

          this.logger.verbose({
            msg: 'Shifting target step below',
            data: { stepId: targetNextStep.id, prevId: movingStep.id },
          });

          targetNextStep.prevId = movingStep.id;
          await entityManager.save(FlowProcessStep, targetNextStep);
        } else {
          this.logger.verbose({ msg: 'No target next step found' });
        }

        return [movingStep, nextStep, targetNextStep, targetParentStep];
      });

    this.logger.log({
      msg: 'Step moved',
      data: {
        incomingData: { stepId, processId, dto },
        modifiedStep: movingStep,
        modifiedData: { nextStep, targetNextStep, targetParentStep },
      },
    });

    return movingStep;
  }
}
