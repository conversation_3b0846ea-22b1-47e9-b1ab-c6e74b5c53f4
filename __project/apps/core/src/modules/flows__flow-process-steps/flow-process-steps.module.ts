import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '@core/database';
import { FlowProcessStepsController } from './flow-process-steps.controller';
import { FlowProcessStepsService } from './flow-process-steps.service';
import {
  CreateFlowProcessStepUseCase,
  DeleteFlowProcessStepByIdUseCase,
  GetAllFlowProcessStepsUseCase,
  GetFlowProcessStepByIdUseCase,
  MoveFlowProcessStepUseCase,
  UpdateFlowProcessStepUseCase,
} from './use-cases';

@Module({
  imports: [DatabaseModule],
  providers: [
    FlowProcessStepsService,

    CreateFlowProcessStepUseCase,
    DeleteFlowProcessStepByIdUseCase,
    GetAllFlowProcessStepsUseCase,
    GetFlowProcessStepByIdUseCase,
    MoveFlowProcessStepUseCase,
    UpdateFlowProcessStepUseCase,
  ],
  controllers: [FlowProcessStepsController],
})
export class FlowProcessStepsModule {}
