import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager } from 'typeorm';
import { FlowProcessStep } from '@core/database';
import { FlowProcessStepsService } from './flow-process-steps.service';

describe('FlowProcessStepsService', () => {
  let service: FlowProcessStepsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FlowProcessStepsService],
    }).compile();

    service = module.get(FlowProcessStepsService);
  });

  describe('normalizeEntityValues', () => {
    it('should set description to null if it is an empty string', () => {
      const flow = new FlowProcessStep();
      flow.description = '';
      const normalizedFlow = service.normalizeEntityValues(flow, {});
      expect(normalizedFlow.description).toBeNull();
    });
  });

  describe('generateUniqueMaterializedPath', () => {
    it('should generate unique mPath', async () => {
      const entityManager = {
        findOne: jest.fn().mockResolvedValue(null),
      };

      const mPath = await service.generateUniqueMaterializedPath(entityManager as unknown as EntityManager, {
        processId: '1',
      });

      expect(entityManager.findOne).toHaveBeenCalledTimes(1);
      expect(mPath).toBeDefined();
    });

    it('should generate unique mPath recursively if it is not unique', async () => {
      const entityManager = {
        findOne: jest.fn().mockResolvedValueOnce({ id: 1 }).mockResolvedValueOnce(null),
      };

      const mPath = await service.generateUniqueMaterializedPath(entityManager as unknown as EntityManager, {
        processId: '1',
      });

      expect(entityManager.findOne).toHaveBeenCalledTimes(2);
      expect(mPath).toBeDefined();
    });
  });
});
