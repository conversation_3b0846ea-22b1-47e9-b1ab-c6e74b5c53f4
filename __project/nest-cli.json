{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/core/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/core/tsconfig.app.json"}, "monorepo": true, "root": "apps/core", "projects": {"chats": {"type": "application", "root": "apps/chats", "entryFile": "main", "sourceRoot": "apps/chats/src", "compilerOptions": {"tsConfigPath": "apps/chats/tsconfig.app.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "core": {"type": "application", "root": "apps/core", "entryFile": "main", "sourceRoot": "apps/core/src", "compilerOptions": {"tsConfigPath": "apps/core/tsconfig.app.json"}}, "redis": {"type": "library", "root": "libs/redis", "entryFile": "index", "sourceRoot": "libs/redis/src", "compilerOptions": {"tsConfigPath": "libs/redis/tsconfig.lib.json"}}}}