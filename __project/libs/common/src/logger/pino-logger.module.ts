import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerModule } from 'nestjs-pino';
import { getPinoHttpOptions } from './pino-config';

@Module({})
export class PinoLoggerModule {
  static forRootAsync(serviceName: string) {
    return LoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        const pinoHttp = getPinoHttpOptions(config, serviceName);
        return { pinoHttp };
      },
    });
  }
}
