import { HttpException, HttpStatus } from '@nestjs/common';

export type ApiResponsePayload<T = undefined> = {
  success: true;
  statusCode: HttpStatus;
  data: T;
  message: string;
};

export type ApiErrorPayload = {
  success: false;
  statusCode: HttpStatus;
  error: string;
  message: string;
  details?: unknown;
  timestamp?: string;
};

export const formatHttpException = (error: HttpException, cause: string = 'ERROR'): ApiErrorPayload => {
  return {
    success: false,
    statusCode: error.getStatus(),
    error: (error.cause as string) || cause,
    message: error.message,
  };
};

export type HttpResponseOptions<T = undefined> = {
  statusCode?: HttpStatus; // TODO: make required
  message?: string;
  data?: T;
};

export class HttpResponse<T = undefined> {
  constructor(options: HttpResponseOptions<T>) {
    const { statusCode = HttpStatus.OK, message = 'Ok', data } = options;
    const response: ApiResponsePayload<T> = {
      success: true,
      statusCode,
      message,
      data: data as T,
    };
    return response;
  }
}
