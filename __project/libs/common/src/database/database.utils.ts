import { BadRequestException, HttpException } from '@nestjs/common';
import { isNumeric } from '../utils';

export const NumericId = <T extends EntityId | null, R extends T extends null ? null : NumericEntityId>(
  id: T,
  exeption: HttpException = new BadRequestException('Invalid id', 'INVALID_ID'),
): R => {
  if (id !== null) {
    if (typeof id === 'string' && !isNumeric(id)) throw exeption;
    return Number(id) as R;
  } else {
    return null as R;
  }
};
