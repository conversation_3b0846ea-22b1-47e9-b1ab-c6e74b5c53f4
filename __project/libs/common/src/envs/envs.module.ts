import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Schema } from 'zod';

@Global()
@Module({})
export class EnvsModule {
  static forRoot<T extends object>(params: { schema: Schema<T>; envFilePath: string | string[] }) {
    const { schema, envFilePath } = params;

    return ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      envFilePath,
      validate: config => schema.parse(config),
    });
  }
}
