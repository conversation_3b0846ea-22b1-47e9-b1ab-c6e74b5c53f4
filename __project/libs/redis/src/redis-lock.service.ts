import { ConflictException, Logger } from '@nestjs/common';
import { Redis } from 'ioredis';
import Redlock, { Lock, ResourceLockedError } from 'redlock';

/**
 * Service for managing locks using Redis.
 *
 * Problem solved:
 * - Concurrent requests to the same resource
 * - Concurrent requests to different resources
 *
 * Be careful!
 * If you use acquireLock function and throw an error you must release the lock manually
 */
export class RedisLockService {
  private readonly logger = new Logger(RedisLockService.name);

  private redlock: Redlock;

  constructor(redis: Redis) {
    this.redlock = new Redlock([redis], {
      retryCount: 5,
      retryDelay: 200, // ms
      retryJitter: 100, // ms
    });

    this.redlock.on('error', (error: unknown) => {
      if (error instanceof ResourceLockedError) return;
      this.logger.error({ msg: 'A Redis client error occurred', error });
    });
  }

  async acquireLock(resource: string, ttl: number = 5000): Promise<Lock> {
    try {
      const lock = await this.redlock.acquire([resource], ttl);
      return lock;
    } catch {
      this.logger.error({ msg: 'Redis: Failed to acquire lock', data: [resource, ttl] });
      throw new ConflictException('RESOURCE_LOCKED');
    }
  }

  async releaseLock(lock: Lock): Promise<void> {
    try {
      await lock.release();
    } catch (error: unknown) {
      this.logger.warn({ msg: 'Failed to release lock', data: { error } });
    }
  }

  async useLock<T>(cb: (lock: Lock) => Promise<T>, resource: string, ttl: number = 5000): Promise<T> {
    const lock = await this.acquireLock(resource, ttl);
    try {
      return await cb(lock);
    } finally {
      await this.releaseLock(lock);
    }
  }
}
