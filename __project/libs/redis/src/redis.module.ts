import { DynamicModule, Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { RedisLockService } from './redis-lock.service';
import { setRedisLockService } from './redis.module.context';

export const REDIS_CLIENT = Symbol('REDIS_CLIENT');

export const REDIS_SHUTDOWN_SERVICE = Symbol('REDIS_SHUTDOWN_SERVICE');

@Global()
@Module({})
export class RedisModule {
  static forRootAsync(): DynamicModule {
    return {
      module: RedisModule,
      providers: [
        {
          provide: REDIS_CLIENT,
          inject: [ConfigService],
          useFactory: async (config: ConfigService): Promise<Redis> => {
            const client: Redis = new Redis({
              host: config.get('REDIS_HOST'),
              port: config.get('REDIS_PORT'),
            });

            await new Promise<void>((resolve, reject) => {
              client.once('ready', resolve);
              client.once('error', reject);
            });

            return client;
          },
        },

        {
          provide: RedisLockService,
          inject: [REDIS_CLIENT],
          useFactory: (redis: Redis) => {
            const lockService = new RedisLockService(redis);
            setRedisLockService(lockService);
            return lockService;
          },
        },

        {
          provide: REDIS_SHUTDOWN_SERVICE,
          inject: [REDIS_CLIENT],
          useFactory: (redis: Redis) => {
            return {
              async onApplicationShutdown() {
                await redis.quit();
              },
            };
          },
        },
      ],
      exports: [REDIS_CLIENT, RedisLockService],
    };
  }
}
