{"name": "@easy-flow/source", "version": "0.0.0", "scripts": {}, "private": true, "workspaces": ["apps/*", "libs/*"], "dependencies": {"@elastic/ecs-pino-format": "^1.5.0", "@nestjs/common": "^11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/platform-express": "^11.1.6", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "axios": "^1.11.0", "ioredis": "^5.7.0", "nanoid": "^3.3.11", "nestjs-pino": "^4.4.0", "nestjs-zod": "^4.3.1", "pg": "^8.16.3", "pino": "^9.9.5", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "typeorm": "^0.3.26", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.35.0", "@nestjs/schematics": "^11.0.7", "@nestjs/testing": "^11.1.6", "@nx/eslint": "21.5.2", "@nx/eslint-plugin": "21.5.2", "@nx/jest": "21.5.2", "@nx/js": "21.5.2", "@nx/nest": "^21.5.2", "@nx/node": "21.5.2", "@nx/web": "21.5.2", "@nx/webpack": "21.5.2", "@swc-node/register": "~1.11.1", "@swc/core": "~1.13.5", "@swc/helpers": "~0.5.17", "@swc/jest": "~0.2.39", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^30.0.0", "@types/node": "24.3.1", "eslint": "^9.35.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "jest": "^30.1.3", "jest-environment-node": "^30.1.2", "jest-util": "^30.0.5", "nx": "21.5.2", "pino-pretty": "^13.1.1", "prettier": "^3.6.2", "ts-jest": "^29.4.1", "ts-node": "10.9.2", "tslib": "^2.8.1", "typescript": "~5.9.2", "typescript-eslint": "^8.43.0", "webpack-cli": "^6.0.1"}}