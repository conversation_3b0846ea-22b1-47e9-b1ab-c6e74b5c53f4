{"name": "@easy-flow/scrum-hub", "version": "0.0.1", "private": true, "nx": {"targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/scrum-hub"}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "prune-lockfile": {"dependsOn": ["build"], "cache": true, "executor": "@nx/js:prune-lockfile", "outputs": ["{workspaceRoot}/apps/scrum-hub/dist/package.json", "{workspaceRoot}/apps/scrum-hub/dist/package-lock.json"], "options": {"buildTarget": "build"}}, "copy-workspace-modules": {"dependsOn": ["build"], "cache": true, "outputs": ["{workspaceRoot}/apps/scrum-hub/dist/workspace_modules"], "executor": "@nx/js:copy-workspace-modules", "options": {"buildTarget": "build"}}, "prune": {"dependsOn": ["prune-lockfile", "copy-workspace-modules"], "executor": "nx:noop"}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@easy-flow/scrum-hub:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@easy-flow/scrum-hub:build:development"}, "production": {"buildTarget": "@easy-flow/scrum-hub:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}, "dependencies": {}}