{"name": "@easy-flow/api-gateway-e2e", "version": "0.0.1", "private": true, "nx": {"implicitDependencies": ["@easy-flow/api-gateway"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{projectRoot}/test-output/jest/coverage"], "options": {"jestConfig": "apps/api-gateway-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["@easy-flow/api-gateway:build", "@easy-flow/api-gateway:serve"]}}}}