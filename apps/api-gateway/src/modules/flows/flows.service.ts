import { Injectable } from '@nestjs/common';
import { Flow } from '@/database';
import { FlowModel } from '@libs/models';

@Injectable()
export class FlowsService {
  constructor() {}

  /**
   * Removes undefined values from the values object and then assigns the values to the entity.
   * !!! This function mutates the entity
   */
  public normalizeEntityValues(entity: Flow, values: Partial<Flow | FlowModel>): Flow {
    // exclude undefined values
    const normalizedValues = { ...values };
    for (const key in normalizedValues) {
      type Key = keyof typeof normalizedValues;
      if (normalizedValues[key as Key] === undefined) {
        delete normalizedValues[key as Key];
      }
    }

    // assign values
    Object.assign(entity, normalizedValues);
    if (entity.description === '') entity.description = null;

    return entity;
  }
}
