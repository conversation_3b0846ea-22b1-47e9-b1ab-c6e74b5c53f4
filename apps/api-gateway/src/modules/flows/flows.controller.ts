import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiOperation, ApiParam } from '@nestjs/swagger';
import { z } from 'zod';
import {
  ApiError,
  ApiSuccessfulResponse,
  EMPTY_RESPONSE,
  HttpResponse,
  SanitizeResponseWithZod,
} from '@libs/common/api';
import { FlowSchema } from '@libs/models';
import { CreateFlowDto, FlowDto, MoveFlowDto, PartialUpdateFlowDto, UpdateFlowDto } from './flows.dto';
import {
  CreateFlowUseCase,
  DeleteFlowByIdUseCase,
  GetAllFlowsUseCase,
  GetFlowByIdUseCase,
  MoveFlowUseCase,
  UpdateFlowUseCase,
} from './use-cases';

const ApiParamFlowId = () =>
  ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' });

@Controller('flows')
export class FlowsController {
  constructor(
    private readonly createFlowUseCase: CreateFlowUseCase,
    private readonly updateFlowUseCase: UpdateFlowUseCase,
    private readonly deleteFlowByIdUseCase: DeleteFlowByIdUseCase,
    private readonly moveFlowUseCase: MoveFlowUseCase,
    private readonly getFlowByIdUseCase: GetFlowByIdUseCase,
    private readonly getAllFlowsUseCase: GetAllFlowsUseCase,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create flow' })
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Flow created', FlowDto)
  @SanitizeResponseWithZod(FlowSchema)
  async createFlow(@Body() dto: CreateFlowDto) {
    const flow = await this.createFlowUseCase.execute({ dto });
    return new HttpResponse({ statusCode: HttpStatus.CREATED, data: flow, message: 'Flow created' });
  }

  @Put(':flowId')
  @ApiOperation({ summary: 'Update flow' })
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async updateFlow(@Param('flowId') flowId: string, @Body() dto: UpdateFlowDto) {
    const flow = await this.updateFlowUseCase.execute({ flowId, dto });
    return new HttpResponse({ data: flow, message: 'Flow updated' });
  }

  @Patch(':flowId')
  @ApiOperation({ summary: 'Partial update flow' })
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async partialUpdateFlow(@Param('flowId') flowId: string, @Body() dto: PartialUpdateFlowDto) {
    await this.updateFlowUseCase.execute({ flowId, dto });
    return new HttpResponse({ message: 'Flow updated' });
  }

  @Delete(':flowId')
  @ApiOperation({ summary: 'Delete flow' })
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow deleted', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async deleteFlow(@Param('flowId') flowId: string) {
    await this.deleteFlowByIdUseCase.execute({ flowId });
    return new HttpResponse({ message: 'Flow deleted' });
  }

  @Post(':flowId/move')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Move flow' })
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow moved', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async moveFlow(@Param('flowId') flowId: string, @Body() dto: MoveFlowDto) {
    await this.moveFlowUseCase.execute({ flowId, dto });
    return new HttpResponse({ message: 'Flow moved' });
  }

  @Get(':flowId')
  @ApiOperation({ summary: 'Get flow' })
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow found', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async getFlow(@Param('flowId') flowId: string) {
    const flow = await this.getFlowByIdUseCase.execute({ flowId });
    return new HttpResponse({ data: flow, message: 'Flow found' });
  }

  @Get()
  @ApiOperation({ summary: 'Get all flows' })
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flows found', [FlowDto])
  @SanitizeResponseWithZod(z.array(FlowSchema))
  async getAllFlows() {
    const flows = await this.getAllFlowsUseCase.execute();
    return new HttpResponse({ data: flows, message: 'Flows found' });
  }
}
