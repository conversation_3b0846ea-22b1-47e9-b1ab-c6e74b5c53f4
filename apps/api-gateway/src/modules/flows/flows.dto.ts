import { createZodDto } from 'nestjs-zod';
import { FlowSchema } from '@libs/models';

export class FlowDto extends createZodDto(FlowSchema) {}

const CreateFlowSchema = FlowSchema.pick({
  name: true,
  description: true,
  prevId: true,
});

export class Create<PERSON>lowDto extends createZodDto(CreateFlowSchema) {}

const UpdateFlowSchema = FlowSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowDto extends createZodDto(UpdateFlowSchema) {}

const PartialUpdateFlowSchema = UpdateFlowSchema.partial();

export class PartialUpdateFlowDto extends createZodDto(PartialUpdateFlowSchema) {}

const MoveFlowSchema = FlowSchema.pick({
  prevId: true,
});

export class MoveFlowDto extends createZodDto(MoveFlowSchema) {}
