import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { FlowProcessStepModel } from '@libs/models';
import { FlowProcess } from './flow-process.entity';

@Entity('flow-process-steps')
export class FlowProcessStep implements UnknownProperties<FlowProcessStepModel> {
  @PrimaryGeneratedColumn()
  id: NumericEntityId;

  // // TODO
  // // @Column({ type: 'varchar' })
  // // type: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'integer', nullable: true })
  parentId: NumericEntityId | null;

  @Column({ type: 'integer', nullable: true })
  prevId: NumericEntityId | null;

  @Column({ type: 'varchar' })
  mPath: string;

  @ManyToOne(() => FlowProcess, process => process.steps, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'processId', referencedColumnName: 'id' })
  flowProcess: FlowProcess;
}
