{"name": "@easy-flow/api-gateway", "version": "0.0.1", "private": true, "nx": {"targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/api-gateway"}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@easy-flow/api-gateway:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@easy-flow/api-gateway:build:development"}, "production": {"buildTarget": "@easy-flow/api-gateway:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}, "dependencies": {}}